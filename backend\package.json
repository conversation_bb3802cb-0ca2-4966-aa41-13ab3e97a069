{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^2.3.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "datauri": "^4.1.0", "dotenv": "^16.4.5", "express": "^4.19.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.4.1", "multer": "^1.4.5-lts.1", "nodemon": "^3.1.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.7", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.6"}}